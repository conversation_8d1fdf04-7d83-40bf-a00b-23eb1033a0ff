{"generatedAt": "2025-07-15T16:46:29.568Z", "totalVendors": 10, "vendorResearch": [{"vendorName": "Canna River", "priority": "HIGH", "searchStrategies": ["Google search: \"Canna River contact information\"", "Google search: \"Canna River wholesale contact\"", "Google search: \"Canna River returns policy\"", "Check business directories (BBB, Yellow Pages)", "Search LinkedIn for company page", "Check industry trade associations"], "potentialWebsites": ["https://cannariver.com", "https://www.cannariver.com", "https://canna-river.com", "https://www.canna-river.com"], "informationNeeded": ["Primary contact email", "Customer service phone number", "Business address", "Return policy URL", "Business hours", "Wholesale/B2B contact if different"], "searchResults": {"vendorName": "Canna River", "searchAttempts": 3, "foundWebsite": null, "contactInfo": {"emails": [], "phones": [], "addresses": [], "website": null}, "searchResults": [{"url": "https://cannariver.com", "accessible": false, "contactInfo": null, "error": "Manual verification needed", "manualSteps": ["1. V<PERSON>t https://cannariver.com", "2. Look for contact/about pages", "3. Extract email, phone, address information", "4. Update vendor database with findings"]}, {"url": "https://www.cannariver.com", "accessible": false, "contactInfo": null, "error": "Manual verification needed", "manualSteps": ["1. V<PERSON><PERSON> https://www.cannariver.com", "2. Look for contact/about pages", "3. Extract email, phone, address information", "4. Update vendor database with findings"]}, {"url": "https://canna-river.com", "accessible": false, "contactInfo": null, "error": "Manual verification needed", "manualSteps": ["1. V<PERSON><PERSON> https://canna-river.com", "2. Look for contact/about pages", "3. Extract email, phone, address information", "4. Update vendor database with findings"]}]}}, {"vendorName": "GRAV®", "priority": "HIGH", "searchStrategies": ["Google search: \"GRAV® contact information\"", "Google search: \"GRAV® wholesale contact\"", "Google search: \"GRAV® returns policy\"", "Check business directories (BBB, Yellow Pages)", "Search LinkedIn for company page", "Check industry trade associations"], "potentialWebsites": ["https://grav®.com", "https://www.grav®.com", "https://grav.com"], "informationNeeded": ["Primary contact email", "Customer service phone number", "Business address", "Return policy URL", "Business hours", "Wholesale/B2B contact if different"], "searchResults": {"vendorName": "GRAV®", "searchAttempts": 3, "foundWebsite": null, "contactInfo": {"emails": [], "phones": [], "addresses": [], "website": null}, "searchResults": [{"url": "https://grav®.com", "accessible": false, "contactInfo": null, "error": "Manual verification needed", "manualSteps": ["1. Visit https://grav®.com", "2. Look for contact/about pages", "3. Extract email, phone, address information", "4. Update vendor database with findings"]}, {"url": "https://www.grav®.com", "accessible": false, "contactInfo": null, "error": "Manual verification needed", "manualSteps": ["1. V<PERSON><PERSON> https://www.grav®.com", "2. Look for contact/about pages", "3. Extract email, phone, address information", "4. Update vendor database with findings"]}, {"url": "https://grav.com", "accessible": false, "contactInfo": null, "error": "Manual verification needed", "manualSteps": ["1. V<PERSON>t https://grav.com", "2. Look for contact/about pages", "3. Extract email, phone, address information", "4. Update vendor database with findings"]}]}}, {"vendorName": "<PERSON>'s Wellness", "priority": "MEDIUM", "searchStrategies": ["Google search: \"<PERSON>'s Wellness contact information\"", "Google search: \"Sonny's Wellness wholesale contact\"", "Google search: \"Sonny's Wellness returns policy\"", "Check business directories (BBB, Yellow Pages)", "Search LinkedIn for company page", "Check industry trade associations"], "potentialWebsites": ["https://sonny'swellness.com", "https://www.sonny'swellness.com", "https://sonny's-wellness.com", "https://www.sonny's-wellness.com", "https://sonnyswellness.com"], "informationNeeded": ["Primary contact email", "Customer service phone number", "Business address", "Return policy URL", "Business hours", "Wholesale/B2B contact if different"], "searchResults": {"vendorName": "<PERSON>'s Wellness", "searchAttempts": 3, "foundWebsite": null, "contactInfo": {"emails": [], "phones": [], "addresses": [], "website": null}, "searchResults": [{"url": "https://sonny'swellness.com", "accessible": false, "contactInfo": null, "error": "Manual verification needed", "manualSteps": ["1. V<PERSON>t https://sonny'swellness.com", "2. Look for contact/about pages", "3. Extract email, phone, address information", "4. Update vendor database with findings"]}, {"url": "https://www.sonny'swellness.com", "accessible": false, "contactInfo": null, "error": "Manual verification needed", "manualSteps": ["1. V<PERSON><PERSON> https://www.sonny'swellness.com", "2. Look for contact/about pages", "3. Extract email, phone, address information", "4. Update vendor database with findings"]}, {"url": "https://sonny's-wellness.com", "accessible": false, "contactInfo": null, "error": "Manual verification needed", "manualSteps": ["1. V<PERSON><PERSON> https://sonny's-wellness.com", "2. Look for contact/about pages", "3. Extract email, phone, address information", "4. Update vendor database with findings"]}]}}, {"vendorName": "Smoke Drop", "priority": "HIGH", "searchStrategies": ["Google search: \"Smoke Drop contact information\"", "Google search: \"Smoke Drop wholesale contact\"", "Google search: \"Smoke Drop returns policy\"", "Check business directories (BBB, Yellow Pages)", "Search LinkedIn for company page", "Check industry trade associations"], "potentialWebsites": ["https://smokedrop.com", "https://www.smokedrop.com", "https://smoke-drop.com", "https://www.smoke-drop.com"], "informationNeeded": ["Primary contact email", "Customer service phone number", "Business address", "Return policy URL", "Business hours", "Wholesale/B2B contact if different"], "searchResults": {"vendorName": "Smoke Drop", "searchAttempts": 3, "foundWebsite": null, "contactInfo": {"emails": [], "phones": [], "addresses": [], "website": null}, "searchResults": [{"url": "https://smokedrop.com", "accessible": false, "contactInfo": null, "error": "Manual verification needed", "manualSteps": ["1. V<PERSON><PERSON> https://smokedrop.com", "2. Look for contact/about pages", "3. Extract email, phone, address information", "4. Update vendor database with findings"]}, {"url": "https://www.smokedrop.com", "accessible": false, "contactInfo": null, "error": "Manual verification needed", "manualSteps": ["1. <PERSON><PERSON><PERSON> https://www.smokedrop.com", "2. Look for contact/about pages", "3. Extract email, phone, address information", "4. Update vendor database with findings"]}, {"url": "https://smoke-drop.com", "accessible": false, "contactInfo": null, "error": "Manual verification needed", "manualSteps": ["1. <PERSON><PERSON><PERSON> https://smoke-drop.com", "2. Look for contact/about pages", "3. Extract email, phone, address information", "4. Update vendor database with findings"]}]}}, {"vendorName": "ASH", "priority": "MEDIUM", "searchStrategies": ["Google search: \"ASH contact information\"", "Google search: \"ASH wholesale contact\"", "Google search: \"ASH returns policy\"", "Check business directories (BBB, Yellow Pages)", "Search LinkedIn for company page", "Check industry trade associations"], "potentialWebsites": ["https://ash.com", "https://www.ash.com"], "informationNeeded": ["Primary contact email", "Customer service phone number", "Business address", "Return policy URL", "Business hours", "Wholesale/B2B contact if different"], "searchResults": {"vendorName": "ASH", "searchAttempts": 2, "foundWebsite": null, "contactInfo": {"emails": [], "phones": [], "addresses": [], "website": null}, "searchResults": [{"url": "https://ash.com", "accessible": false, "contactInfo": null, "error": "Manual verification needed", "manualSteps": ["1. V<PERSON><PERSON> https://ash.com", "2. Look for contact/about pages", "3. Extract email, phone, address information", "4. Update vendor database with findings"]}, {"url": "https://www.ash.com", "accessible": false, "contactInfo": null, "error": "Manual verification needed", "manualSteps": ["1. <PERSON><PERSON><PERSON> https://www.ash.com", "2. Look for contact/about pages", "3. Extract email, phone, address information", "4. Update vendor database with findings"]}]}}, {"vendorName": "Discreet Smoker", "priority": "LOW", "searchStrategies": ["Google search: \"Discreet Smoker contact information\"", "Google search: \"Discreet Smoker wholesale contact\"", "Google search: \"Discreet Smoker returns policy\"", "Check business directories (BBB, Yellow Pages)", "Search LinkedIn for company page", "Check industry trade associations"], "potentialWebsites": ["https://discreetsmoker.com", "https://www.discreetsmoker.com", "https://discreet-smoker.com", "https://www.discreet-smoker.com"], "informationNeeded": ["Primary contact email", "Customer service phone number", "Business address", "Return policy URL", "Business hours", "Wholesale/B2B contact if different"], "searchResults": {"vendorName": "Discreet Smoker", "searchAttempts": 3, "foundWebsite": null, "contactInfo": {"emails": [], "phones": [], "addresses": [], "website": null}, "searchResults": [{"url": "https://discreetsmoker.com", "accessible": false, "contactInfo": null, "error": "Manual verification needed", "manualSteps": ["1. V<PERSON><PERSON> https://discreetsmoker.com", "2. Look for contact/about pages", "3. Extract email, phone, address information", "4. Update vendor database with findings"]}, {"url": "https://www.discreetsmoker.com", "accessible": false, "contactInfo": null, "error": "Manual verification needed", "manualSteps": ["1. <PERSON><PERSON><PERSON> https://www.discreetsmoker.com", "2. Look for contact/about pages", "3. Extract email, phone, address information", "4. Update vendor database with findings"]}, {"url": "https://discreet-smoker.com", "accessible": false, "contactInfo": null, "error": "Manual verification needed", "manualSteps": ["1. <PERSON><PERSON><PERSON> https://discreet-smoker.com", "2. Look for contact/about pages", "3. Extract email, phone, address information", "4. Update vendor database with findings"]}]}}, {"vendorName": "FlexCBD", "priority": "LOW", "searchStrategies": ["Google search: \"FlexCBD contact information\"", "Google search: \"FlexCBD wholesale contact\"", "Google search: \"FlexCBD returns policy\"", "Check business directories (BBB, Yellow Pages)", "Search LinkedIn for company page", "Check industry trade associations"], "potentialWebsites": ["https://flexcbd.com", "https://www.flexcbd.com"], "informationNeeded": ["Primary contact email", "Customer service phone number", "Business address", "Return policy URL", "Business hours", "Wholesale/B2B contact if different"], "searchResults": {"vendorName": "FlexCBD", "searchAttempts": 2, "foundWebsite": null, "contactInfo": {"emails": [], "phones": [], "addresses": [], "website": null}, "searchResults": [{"url": "https://flexcbd.com", "accessible": false, "contactInfo": null, "error": "Manual verification needed", "manualSteps": ["1. V<PERSON>t https://flexcbd.com", "2. Look for contact/about pages", "3. Extract email, phone, address information", "4. Update vendor database with findings"]}, {"url": "https://www.flexcbd.com", "accessible": false, "contactInfo": null, "error": "Manual verification needed", "manualSteps": ["1. V<PERSON><PERSON> https://www.flexcbd.com", "2. Look for contact/about pages", "3. Extract email, phone, address information", "4. Update vendor database with findings"]}]}}, {"vendorName": "<PERSON><PERSON><PERSON>", "priority": "LOW", "searchStrategies": ["Google search: \"<PERSON>.pen contact information\"", "Google search: \"<PERSON>.pen wholesale contact\"", "Google search: \"O.pen returns policy\"", "Check business directories (BBB, Yellow Pages)", "Search LinkedIn for company page", "Check industry trade associations"], "potentialWebsites": ["https://o.pen.com", "https://www.o.pen.com", "https://open.com"], "informationNeeded": ["Primary contact email", "Customer service phone number", "Business address", "Return policy URL", "Business hours", "Wholesale/B2B contact if different"], "searchResults": {"vendorName": "<PERSON><PERSON><PERSON>", "searchAttempts": 3, "foundWebsite": null, "contactInfo": {"emails": [], "phones": [], "addresses": [], "website": null}, "searchResults": [{"url": "https://o.pen.com", "accessible": false, "contactInfo": null, "error": "Manual verification needed", "manualSteps": ["1. V<PERSON><PERSON> https://o.pen.com", "2. Look for contact/about pages", "3. Extract email, phone, address information", "4. Update vendor database with findings"]}, {"url": "https://www.o.pen.com", "accessible": false, "contactInfo": null, "error": "Manual verification needed", "manualSteps": ["1. <PERSON><PERSON><PERSON> https://www.o.pen.com", "2. Look for contact/about pages", "3. Extract email, phone, address information", "4. Update vendor database with findings"]}, {"url": "https://open.com", "accessible": false, "contactInfo": null, "error": "Manual verification needed", "manualSteps": ["1. <PERSON><PERSON><PERSON> https://open.com", "2. Look for contact/about pages", "3. Extract email, phone, address information", "4. Update vendor database with findings"]}]}}, {"vendorName": "Ongrok", "priority": "LOW", "searchStrategies": ["Google search: \"Ongrok contact information\"", "Google search: \"Ongrok wholesale contact\"", "Google search: \"Ongrok returns policy\"", "Check business directories (BBB, Yellow Pages)", "Search LinkedIn for company page", "Check industry trade associations"], "potentialWebsites": ["https://ongrok.com", "https://www.ongrok.com"], "informationNeeded": ["Primary contact email", "Customer service phone number", "Business address", "Return policy URL", "Business hours", "Wholesale/B2B contact if different"], "searchResults": {"vendorName": "Ongrok", "searchAttempts": 2, "foundWebsite": null, "contactInfo": {"emails": [], "phones": [], "addresses": [], "website": null}, "searchResults": [{"url": "https://ongrok.com", "accessible": false, "contactInfo": null, "error": "Manual verification needed", "manualSteps": ["1. V<PERSON>t https://ongrok.com", "2. Look for contact/about pages", "3. Extract email, phone, address information", "4. Update vendor database with findings"]}, {"url": "https://www.ongrok.com", "accessible": false, "contactInfo": null, "error": "Manual verification needed", "manualSteps": ["1. V<PERSON><PERSON> https://www.ongrok.com", "2. Look for contact/about pages", "3. Extract email, phone, address information", "4. Update vendor database with findings"]}]}}, {"vendorName": "<PERSON><PERSON><PERSON>", "priority": "LOW", "searchStrategies": ["Google search: \"<PERSON><PERSON><PERSON> contact information\"", "Google search: \"Vessel wholesale contact\"", "Google search: \"V<PERSON>el returns policy\"", "Check business directories (BBB, Yellow Pages)", "Search LinkedIn for company page", "Check industry trade associations"], "potentialWebsites": ["https://vessel.com", "https://www.vessel.com"], "informationNeeded": ["Primary contact email", "Customer service phone number", "Business address", "Return policy URL", "Business hours", "Wholesale/B2B contact if different"], "searchResults": {"vendorName": "<PERSON><PERSON><PERSON>", "searchAttempts": 2, "foundWebsite": null, "contactInfo": {"emails": [], "phones": [], "addresses": [], "website": null}, "searchResults": [{"url": "https://vessel.com", "accessible": false, "contactInfo": null, "error": "Manual verification needed", "manualSteps": ["1. V<PERSON>t https://vessel.com", "2. Look for contact/about pages", "3. Extract email, phone, address information", "4. Update vendor database with findings"]}, {"url": "https://www.vessel.com", "accessible": false, "contactInfo": null, "error": "Manual verification needed", "manualSteps": ["1. V<PERSON><PERSON> https://www.vessel.com", "2. Look for contact/about pages", "3. Extract email, phone, address information", "4. Update vendor database with findings"]}]}}]}