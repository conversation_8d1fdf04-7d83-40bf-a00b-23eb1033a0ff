const fs = require('fs').promises;
require('dotenv').config();

class ExternalVendorLookup {
  constructor() {
    this.vendorSearchStrategies = {
      // Common vendor website patterns
      websitePatterns: [
        (name) => `https://${name.toLowerCase().replace(/\s+/g, '')}.com`,
        (name) => `https://www.${name.toLowerCase().replace(/\s+/g, '')}.com`,
        (name) => `https://${name.toLowerCase().replace(/\s+/g, '-')}.com`,
        (name) => `https://www.${name.toLowerCase().replace(/\s+/g, '-')}.com`,
        (name) => `https://${name.toLowerCase().replace(/[^a-z0-9]/g, '')}.com`
      ],
      
      // Contact page patterns
      contactPatterns: [
        '/contact',
        '/contact-us',
        '/contact-us/',
        '/pages/contact',
        '/pages/contact-us',
        '/support',
        '/help',
        '/about/contact'
      ]
    };
  }

  // Generate potential vendor websites
  generateVendorWebsites(vendorName) {
    const websites = [];
    
    for (const pattern of this.vendorSearchStrategies.websitePatterns) {
      try {
        const url = pattern(vendorName);
        websites.push(url);
      } catch (error) {
        // Skip invalid patterns
      }
    }
    
    return [...new Set(websites)]; // Remove duplicates
  }

  // Extract contact information from website content
  extractContactInfo(content, baseUrl) {
    const contactInfo = {
      emails: [],
      phones: [],
      addresses: [],
      socialMedia: []
    };

    // Email patterns
    const emailRegex = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g;
    const emails = content.match(emailRegex) || [];
    
    // Filter out common non-contact emails
    const filteredEmails = emails.filter(email => {
      const lower = email.toLowerCase();
      return !lower.includes('noreply') && 
             !lower.includes('no-reply') && 
             !lower.includes('example') &&
             !lower.includes('test') &&
             !lower.includes('admin@') &&
             !lower.includes('webmaster@');
    });
    
    contactInfo.emails = [...new Set(filteredEmails)];

    // Phone patterns (US format)
    const phoneRegex = /(?:\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})/g;
    const phones = [];
    let phoneMatch;
    while ((phoneMatch = phoneRegex.exec(content)) !== null) {
      phones.push(phoneMatch[0]);
    }
    contactInfo.phones = [...new Set(phones)];

    // Address patterns (basic US address detection)
    const addressRegex = /\d+\s+[A-Za-z\s]+(?:Street|St|Avenue|Ave|Road|Rd|Boulevard|Blvd|Lane|Ln|Drive|Dr|Circle|Cir|Court|Ct|Place|Pl)\s*,?\s*[A-Za-z\s]+,?\s*[A-Z]{2}\s*\d{5}/gi;
    const addresses = content.match(addressRegex) || [];
    contactInfo.addresses = [...new Set(addresses)];

    return contactInfo;
  }

  // Search for vendor contact information
  async searchVendorContact(vendorName) {
    console.log(`🔍 Searching for ${vendorName} contact information...`);
    
    const potentialWebsites = this.generateVendorWebsites(vendorName);
    const results = {
      vendorName,
      searchAttempts: 0,
      foundWebsite: null,
      contactInfo: {
        emails: [],
        phones: [],
        addresses: [],
        website: null
      },
      searchResults: []
    };

    // Try each potential website
    for (const website of potentialWebsites.slice(0, 3)) { // Limit to first 3 attempts
      try {
        results.searchAttempts++;
        console.log(`   Trying: ${website}`);
        
        // In a real implementation, you would use a web scraping library
        // For now, we'll simulate the process and provide manual lookup guidance
        
        const searchResult = {
          url: website,
          accessible: false,
          contactInfo: null,
          error: 'Manual verification needed'
        };
        
        // Add manual lookup instructions
        searchResult.manualSteps = [
          `1. Visit ${website}`,
          `2. Look for contact/about pages`,
          `3. Extract email, phone, address information`,
          `4. Update vendor database with findings`
        ];
        
        results.searchResults.push(searchResult);
        
      } catch (error) {
        console.log(`   ❌ Error accessing ${website}:`, error.message);
        results.searchResults.push({
          url: website,
          accessible: false,
          error: error.message
        });
      }
    }

    return results;
  }

  // Generate vendor research report
  async generateVendorResearchReport(vendors) {
    console.log('📋 Generating vendor research report...\n');
    
    const report = {
      generatedAt: new Date().toISOString(),
      totalVendors: vendors.length,
      vendorResearch: []
    };

    for (const vendorName of vendors) {
      console.log(`🏭 Researching: ${vendorName}`);
      
      const searchResults = await this.searchVendorContact(vendorName);
      
      // Add manual research guidance
      const researchGuidance = {
        vendorName,
        priority: this.getVendorPriority(vendorName),
        searchStrategies: [
          `Google search: "${vendorName} contact information"`,
          `Google search: "${vendorName} wholesale contact"`,
          `Google search: "${vendorName} returns policy"`,
          `Check business directories (BBB, Yellow Pages)`,
          `Search LinkedIn for company page`,
          `Check industry trade associations`
        ],
        potentialWebsites: this.generateVendorWebsites(vendorName),
        informationNeeded: [
          'Primary contact email',
          'Customer service phone number',
          'Business address',
          'Return policy URL',
          'Business hours',
          'Wholesale/B2B contact if different'
        ],
        searchResults
      };
      
      report.vendorResearch.push(researchGuidance);
      console.log(`   ✅ Research plan created for ${vendorName}\n`);
    }

    return report;
  }

  // Determine vendor priority based on product count and importance
  getVendorPriority(vendorName) {
    const highPriorityVendors = ['Canna River', 'GRAV®', 'Smoke Drop'];
    const mediumPriorityVendors = ['Sonny\'s Wellness', 'ASH'];
    
    if (highPriorityVendors.includes(vendorName)) {
      return 'HIGH';
    } else if (mediumPriorityVendors.includes(vendorName)) {
      return 'MEDIUM';
    } else {
      return 'LOW';
    }
  }

  // Create manual vendor contact form template
  generateVendorContactTemplate() {
    return {
      vendorName: '',
      contactInfo: {
        primaryEmail: '',
        customerServiceEmail: '',
        returnsEmail: '',
        phone: '',
        tollFreePhone: '',
        website: '',
        contactPageUrl: '',
        address: {
          street: '',
          city: '',
          state: '',
          zip: '',
          country: 'USA'
        },
        businessHours: '',
        timeZone: '',
        returnPolicy: {
          url: '',
          timeLimit: '',
          requirements: '',
          restockingFee: ''
        },
        specialInstructions: '',
        lastVerified: new Date().toISOString().split('T')[0],
        verifiedBy: '',
        notes: ''
      }
    };
  }

  // Save research report
  async saveResearchReport(report, filename = 'vendor-research-report.json') {
    try {
      await fs.writeFile(filename, JSON.stringify(report, null, 2));
      console.log(`💾 Research report saved to ${filename}`);
      return filename;
    } catch (error) {
      console.error('Error saving research report:', error.message);
      throw error;
    }
  }

  // Create vendor contact spreadsheet template
  async createVendorContactSpreadsheet(vendors) {
    const csvHeaders = [
      'Vendor Name',
      'Priority',
      'Primary Email',
      'Phone',
      'Website',
      'Street Address',
      'City',
      'State',
      'ZIP',
      'Business Hours',
      'Return Policy URL',
      'Notes',
      'Verified',
      'Last Updated'
    ];

    const csvRows = [csvHeaders.join(',')];
    
    for (const vendorName of vendors) {
      const priority = this.getVendorPriority(vendorName);
      const row = [
        `"${vendorName}"`,
        priority,
        '', // Primary Email - to be filled manually
        '', // Phone - to be filled manually
        '', // Website - to be filled manually
        '', // Street Address - to be filled manually
        '', // City - to be filled manually
        '', // State - to be filled manually
        '', // ZIP - to be filled manually
        '', // Business Hours - to be filled manually
        '', // Return Policy URL - to be filled manually
        '', // Notes - to be filled manually
        'FALSE', // Verified
        new Date().toISOString().split('T')[0] // Last Updated
      ];
      csvRows.push(row.join(','));
    }

    const csvContent = csvRows.join('\n');
    const filename = `vendor-contact-template-${new Date().toISOString().split('T')[0]}.csv`;
    
    try {
      await fs.writeFile(filename, csvContent);
      console.log(`📊 Vendor contact spreadsheet template saved to ${filename}`);
      return filename;
    } catch (error) {
      console.error('Error saving spreadsheet:', error.message);
      throw error;
    }
  }
}

// Main execution
async function main() {
  try {
    const externalLookup = new ExternalVendorLookup();
    
    // Example vendors from your store
    const vendors = [
      'Canna River',
      'GRAV®', 
      'Sonny\'s Wellness',
      'Smoke Drop',
      'ASH',
      'Discreet Smoker',
      'FlexCBD',
      'O.pen',
      'Ongrok',
      'Vessel'
    ];

    console.log('🔍 Starting external vendor research...\n');
    
    // Generate research report
    const report = await externalLookup.generateVendorResearchReport(vendors);
    
    // Save research report
    const reportFile = await externalLookup.saveResearchReport(report);
    
    // Create spreadsheet template
    const spreadsheetFile = await externalLookup.createVendorContactSpreadsheet(vendors);
    
    console.log('\n' + '='.repeat(60));
    console.log('📊 VENDOR RESEARCH SUMMARY');
    console.log('='.repeat(60));
    console.log(`Total Vendors: ${report.totalVendors}`);
    console.log(`Research Report: ${reportFile}`);
    console.log(`Contact Template: ${spreadsheetFile}`);
    console.log(`Generated: ${report.generatedAt}`);
    
    console.log('\n📋 NEXT STEPS:');
    console.log('1. Review the research report for each vendor');
    console.log('2. Manually visit vendor websites and gather contact info');
    console.log('3. Fill in the CSV template with verified contact details');
    console.log('4. Update the vendor-lookup.js database with real information');
    console.log('5. Run add-vendor-metafields.js to add info to Shopify products');
    
  } catch (error) {
    console.error('❌ Error in external vendor research:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = ExternalVendorLookup;
