const { createAdminApiClient } = require('@shopify/admin-api-client');
require('dotenv').config();

async function testShopifyConnection() {
  console.log('🔍 Testing Shopify API Connection...\n');

  // Check environment variables
  console.log('📋 Environment Variables:');
  console.log(`   SHOPIFY_STORE_URL: ${process.env.SHOPIFY_STORE_URL || 'NOT SET'}`);
  console.log(`   SHOPIFY_ACCESS_TOKEN: ${process.env.SHOPIFY_ACCESS_TOKEN ? 'SET (length: ' + process.env.SHOPIFY_ACCESS_TOKEN.length + ')' : 'NOT SET'}`);
  console.log(`   SHOPIFY_API_VERSION: ${process.env.SHOPIFY_API_VERSION || 'NOT SET'}`);
  console.log('');

  if (!process.env.SHOPIFY_STORE_URL || !process.env.SHOPIFY_ACCESS_TOKEN) {
    console.log('❌ Missing required Shopify environment variables');
    return;
  }

  try {
    // Create Shopify client
    console.log('🔗 Creating Shopify API client...');
    const client = createAdminApiClient({
      storeDomain: process.env.SHOPIFY_STORE_URL,
      accessToken: process.env.SHOPIFY_ACCESS_TOKEN,
      apiVersion: process.env.SHOPIFY_API_VERSION || '2025-07'
    });

    console.log('✅ Client created successfully');
    console.log('');

    // Test 1: Simple shop query
    console.log('🏪 Test 1: Basic shop information...');
    const shopQuery = `
      query {
        shop {
          name
          myshopifyDomain
          email
          plan {
            displayName
          }
        }
      }
    `;

    try {
      const response = await client.request(shopQuery);
      console.log('📡 Raw response:', JSON.stringify(response, null, 2));
      
      if (response.data?.shop) {
        console.log('✅ Shop query successful!');
        console.log(`   Shop Name: ${response.data.shop.name}`);
        console.log(`   Domain: ${response.data.shop.myshopifyDomain}`);
        console.log(`   Email: ${response.data.shop.email}`);
        console.log(`   Plan: ${response.data.shop.plan?.displayName || 'Unknown'}`);
      } else {
        console.log('❌ Shop query returned no data');
        console.log('   This might indicate an authentication issue');
      }
    } catch (error) {
      console.log('❌ Shop query failed:', error.message);
      if (error.response) {
        console.log('   Response status:', error.response.status);
        console.log('   Response data:', JSON.stringify(error.response.data, null, 2));
      }
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 2: Products query with full vendor details
    console.log('📦 Test 2: Products query with full vendor details...');
    const productsQuery = `
      query {
        products(first: 3) {
          edges {
            node {
              id
              title
              vendor
              handle
              tags
              productType
              metafields(first: 10) {
                edges {
                  node {
                    namespace
                    key
                    value
                    type
                  }
                }
              }
              variants(first: 5) {
                edges {
                  node {
                    id
                    title
                    sku
                    barcode
                    metafields(first: 5) {
                      edges {
                        node {
                          namespace
                          key
                          value
                          type
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    `;

    try {
      const response = await client.request(productsQuery);
      console.log('📡 Products response (truncated for readability)');

      if (response.data?.products?.edges) {
        const products = response.data.products.edges;
        console.log(`✅ Found ${products.length} products with full details:`);

        products.forEach((productEdge, index) => {
          const product = productEdge.node;
          console.log(`\n   ${index + 1}. ${product.title}`);
          console.log(`      Product ID: ${product.id}`);
          console.log(`      Vendor: ${product.vendor || 'Not specified'}`);
          console.log(`      Handle: ${product.handle}`);
          console.log(`      Product Type: ${product.productType || 'Not specified'}`);
          console.log(`      Tags: ${product.tags.length > 0 ? product.tags.join(', ') : 'None'}`);

          // Show metafields
          if (product.metafields?.edges?.length > 0) {
            console.log(`      Product Metafields:`);
            product.metafields.edges.forEach(metafield => {
              const meta = metafield.node;
              console.log(`        ${meta.namespace}.${meta.key}: ${meta.value} (${meta.type})`);
            });
          } else {
            console.log(`      Product Metafields: None`);
          }

          // Show variants
          if (product.variants?.edges?.length > 0) {
            console.log(`      Variants (${product.variants.edges.length}):`);
            product.variants.edges.forEach((variantEdge, vIndex) => {
              const variant = variantEdge.node;
              console.log(`        ${vIndex + 1}. ${variant.title}`);
              console.log(`           Variant ID: ${variant.id}`);
              console.log(`           SKU: ${variant.sku || 'Not set'}`);
              console.log(`           Barcode: ${variant.barcode || 'Not set'}`);

              if (variant.metafields?.edges?.length > 0) {
                console.log(`           Variant Metafields:`);
                variant.metafields.edges.forEach(metafield => {
                  const meta = metafield.node;
                  console.log(`             ${meta.namespace}.${meta.key}: ${meta.value} (${meta.type})`);
                });
              }
            });
          }
        });

        // Show raw JSON for first product for debugging
        console.log('\n📋 Raw JSON for first product (for debugging):');
        console.log(JSON.stringify(products[0], null, 2));

      } else {
        console.log('❌ Products query returned no data');
      }
    } catch (error) {
      console.log('❌ Products query failed:', error.message);
      if (error.response) {
        console.log('   Response status:', error.response.status);
        console.log('   Response data:', JSON.stringify(error.response.data, null, 2));
      }
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 3: Vendor/Supplier lookup test
    console.log('🏭 Test 3: Vendor/Supplier contact lookup...');

    // Try to find vendor contact information through various methods
    const vendorLookupQuery = `
      query {
        products(first: 5, query: "vendor:Canna River") {
          edges {
            node {
              id
              title
              vendor
              metafields(first: 20, namespace: "vendor") {
                edges {
                  node {
                    namespace
                    key
                    value
                    type
                  }
                }
              }
            }
          }
        }
        metaobjects(first: 10, type: "vendor") {
          edges {
            node {
              id
              type
              handle
              fields {
                key
                value
                type
              }
            }
          }
        }
      }
    `;

    try {
      const response = await client.request(vendorLookupQuery);
      console.log('📡 Vendor lookup response:');

      if (response.data?.products?.edges) {
        const products = response.data.products.edges;
        console.log(`✅ Found ${products.length} Canna River products:`);

        products.forEach((productEdge, index) => {
          const product = productEdge.node;
          console.log(`   ${index + 1}. ${product.title}`);

          if (product.metafields?.edges?.length > 0) {
            console.log(`      Vendor Metafields:`);
            product.metafields.edges.forEach(metafield => {
              const meta = metafield.node;
              console.log(`        ${meta.key}: ${meta.value}`);
            });
          } else {
            console.log(`      No vendor metafields found`);
          }
        });
      }

      if (response.data?.metaobjects?.edges) {
        const metaobjects = response.data.metaobjects.edges;
        console.log(`\n📋 Found ${metaobjects.length} vendor metaobjects:`);

        metaobjects.forEach((metaobjectEdge, index) => {
          const metaobject = metaobjectEdge.node;
          console.log(`   ${index + 1}. Vendor: ${metaobject.handle}`);
          console.log(`      ID: ${metaobject.id}`);

          if (metaobject.fields?.length > 0) {
            console.log(`      Fields:`);
            metaobject.fields.forEach(field => {
              console.log(`        ${field.key}: ${field.value}`);
            });
          }
        });
      } else {
        console.log('\n📋 No vendor metaobjects found');
      }

    } catch (error) {
      console.log('❌ Vendor lookup failed:', error.message);
      console.log('   This might be normal if no vendor metafields/metaobjects exist');
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 4: API permissions check
    console.log('🔐 Test 4: Checking API permissions...');
    const permissionsQuery = `
      query {
        app {
          handle
          installation {
            accessScopes {
              handle
            }
          }
        }
      }
    `;

    try {
      const response = await client.request(permissionsQuery);
      console.log('📡 Permissions response:', JSON.stringify(response, null, 2));
      
      if (response.data?.app?.installation?.accessScopes) {
        const scopes = response.data.app.installation.accessScopes;
        console.log(`✅ App has ${scopes.length} permissions:`);
        scopes.forEach((scope, index) => {
          console.log(`   ${index + 1}. ${scope.handle}`);
        });
      } else {
        console.log('❌ Could not retrieve app permissions');
      }
    } catch (error) {
      console.log('❌ Permissions query failed:', error.message);
      console.log('   This might be normal for private apps');
    }

  } catch (error) {
    console.log('❌ Failed to create Shopify client:', error.message);
    console.log('   Check your store URL and access token');
  }
}

// Run the test
testShopifyConnection().then(() => {
  console.log('\n🏁 Shopify connection test completed!');
}).catch(error => {
  console.error('💥 Test failed:', error.message);
});
