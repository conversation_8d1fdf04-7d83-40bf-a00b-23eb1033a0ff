const { createAdminApiClient } = require('@shopify/admin-api-client');
const VendorLookupService = require('./vendor-lookup');
require('dotenv').config();

class VendorMetafieldManager {
  constructor() {
    this.client = createAdminApiClient({
      storeDomain: process.env.SHOPIFY_STORE_URL,
      accessToken: process.env.SHOPIFY_ACCESS_TOKEN,
      apiVersion: process.env.SHOPIFY_API_VERSION || '2025-07'
    });
    
    this.vendorService = new VendorLookupService();
  }

  // Create metafield definitions for vendor contact information
  async createVendorMetafieldDefinitions() {
    console.log('🔧 Creating vendor metafield definitions...\n');
    
    const metafieldDefinitions = [
      {
        name: 'Vendor Email',
        namespace: 'vendor',
        key: 'email',
        description: 'Primary contact email for the vendor/supplier',
        type: 'single_line_text_field'
      },
      {
        name: 'Vendor Phone',
        namespace: 'vendor',
        key: 'phone',
        description: 'Primary contact phone number for the vendor/supplier',
        type: 'single_line_text_field'
      },
      {
        name: 'Vendor Website',
        namespace: 'vendor',
        key: 'website',
        description: 'Vendor website URL',
        type: 'url'
      },
      {
        name: 'Vendor Address',
        namespace: 'vendor',
        key: 'address',
        description: 'Vendor business address (JSON format)',
        type: 'json'
      },
      {
        name: 'Vendor Business Hours',
        namespace: 'vendor',
        key: 'business_hours',
        description: 'Vendor business hours',
        type: 'single_line_text_field'
      },
      {
        name: 'Vendor Return Policy',
        namespace: 'vendor',
        key: 'return_policy',
        description: 'URL to vendor return policy',
        type: 'url'
      },
      {
        name: 'Vendor Notes',
        namespace: 'vendor',
        key: 'notes',
        description: 'Special notes about vendor return process',
        type: 'multi_line_text_field'
      }
    ];

    const results = [];
    
    for (const definition of metafieldDefinitions) {
      try {
        const mutation = `
          mutation metafieldDefinitionCreate($definition: MetafieldDefinitionInput!) {
            metafieldDefinitionCreate(definition: $definition) {
              createdDefinition {
                id
                name
                namespace
                key
                type {
                  name
                }
              }
              userErrors {
                field
                message
              }
            }
          }
        `;

        const variables = {
          definition: {
            name: definition.name,
            namespace: definition.namespace,
            key: definition.key,
            description: definition.description,
            type: definition.type,
            ownerType: 'PRODUCT'
          }
        };

        const response = await this.client.request(mutation, { variables });
        
        if (response.data?.metafieldDefinitionCreate?.createdDefinition) {
          console.log(`✅ Created: ${definition.name} (${definition.namespace}.${definition.key})`);
          results.push({
            success: true,
            definition: response.data.metafieldDefinitionCreate.createdDefinition
          });
        } else {
          const errors = response.data?.metafieldDefinitionCreate?.userErrors || [];
          console.log(`❌ Failed: ${definition.name} - ${errors.map(e => e.message).join(', ')}`);
          results.push({
            success: false,
            definition: definition,
            errors: errors
          });
        }
      } catch (error) {
        console.log(`❌ Error creating ${definition.name}:`, error.message);
        results.push({
          success: false,
          definition: definition,
          error: error.message
        });
      }
    }
    
    return results;
  }

  // Add vendor contact metafields to products
  async addVendorMetafieldsToProducts() {
    console.log('📦 Adding vendor metafields to products...\n');
    
    // Get all vendors and their products
    const vendors = await this.vendorService.getAllVendors();
    const results = [];
    
    for (const vendorName of vendors) {
      console.log(`🏭 Processing vendor: ${vendorName}`);
      
      const products = await this.vendorService.getProductsByVendor(vendorName);
      const vendorInfo = this.vendorService.getVendorContactInfo(vendorName);
      
      console.log(`   Found ${products.length} products`);
      
      for (const productEdge of products) {
        const product = productEdge.node;
        const productId = product.id;
        
        try {
          // Create metafields for this product
          const metafields = [
            {
              namespace: 'vendor',
              key: 'email',
              value: vendorInfo.email,
              type: 'single_line_text_field'
            },
            {
              namespace: 'vendor',
              key: 'phone',
              value: vendorInfo.phone,
              type: 'single_line_text_field'
            },
            {
              namespace: 'vendor',
              key: 'website',
              value: vendorInfo.website,
              type: 'url'
            },
            {
              namespace: 'vendor',
              key: 'address',
              value: JSON.stringify(vendorInfo.address),
              type: 'json'
            },
            {
              namespace: 'vendor',
              key: 'business_hours',
              value: vendorInfo.businessHours,
              type: 'single_line_text_field'
            },
            {
              namespace: 'vendor',
              key: 'return_policy',
              value: vendorInfo.returnPolicy,
              type: 'url'
            },
            {
              namespace: 'vendor',
              key: 'notes',
              value: vendorInfo.notes,
              type: 'multi_line_text_field'
            }
          ];

          // Add metafields to product
          for (const metafield of metafields) {
            const mutation = `
              mutation productUpdate($input: ProductInput!) {
                productUpdate(input: $input) {
                  product {
                    id
                    title
                  }
                  userErrors {
                    field
                    message
                  }
                }
              }
            `;

            const variables = {
              input: {
                id: productId,
                metafields: [metafield]
              }
            };

            const response = await this.client.request(mutation, { variables });
            
            if (response.data?.productUpdate?.userErrors?.length > 0) {
              console.log(`     ❌ Error adding ${metafield.key}:`, response.data.productUpdate.userErrors);
            }
          }
          
          console.log(`     ✅ Added vendor metafields to: ${product.title}`);
          results.push({
            success: true,
            productId: productId,
            productTitle: product.title,
            vendor: vendorName
          });
          
        } catch (error) {
          console.log(`     ❌ Error processing ${product.title}:`, error.message);
          results.push({
            success: false,
            productId: productId,
            productTitle: product.title,
            vendor: vendorName,
            error: error.message
          });
        }
      }
      
      console.log(`   ✅ Completed ${vendorName}\n`);
    }
    
    return results;
  }

  // Test reading vendor metafields from products
  async testVendorMetafields(productId = null) {
    console.log('🧪 Testing vendor metafields...\n');
    
    const query = `
      query($id: ID!) {
        product(id: $id) {
          id
          title
          vendor
          metafields(namespace: "vendor", first: 10) {
            edges {
              node {
                namespace
                key
                value
                type
              }
            }
          }
        }
      }
    `;

    // If no product ID provided, get the first product
    if (!productId) {
      const products = await this.vendorService.getProductsByVendor('Canna River', 1);
      if (products.length > 0) {
        productId = products[0].node.id;
      } else {
        console.log('❌ No products found to test');
        return;
      }
    }

    try {
      const variables = { id: productId };
      const response = await this.client.request(query, { variables });
      
      if (response.data?.product) {
        const product = response.data.product;
        console.log(`📦 Product: ${product.title}`);
        console.log(`🏭 Vendor: ${product.vendor}`);
        console.log(`📋 Vendor Metafields:`);
        
        if (product.metafields?.edges?.length > 0) {
          product.metafields.edges.forEach(metafieldEdge => {
            const metafield = metafieldEdge.node;
            console.log(`   ${metafield.key}: ${metafield.value}`);
          });
        } else {
          console.log('   No vendor metafields found');
        }
      }
    } catch (error) {
      console.log('❌ Error testing metafields:', error.message);
    }
  }
}

// Main execution
async function main() {
  try {
    const manager = new VendorMetafieldManager();
    
    console.log('🚀 Starting Vendor Metafield Setup...\n');
    
    // Step 1: Create metafield definitions
    console.log('STEP 1: Creating metafield definitions');
    console.log('='.repeat(50));
    const definitionResults = await manager.createVendorMetafieldDefinitions();
    
    const successfulDefinitions = definitionResults.filter(r => r.success).length;
    console.log(`\n✅ Created ${successfulDefinitions}/${definitionResults.length} metafield definitions\n`);
    
    // Step 2: Add metafields to products
    console.log('STEP 2: Adding vendor metafields to products');
    console.log('='.repeat(50));
    const productResults = await manager.addVendorMetafieldsToProducts();
    
    const successfulProducts = productResults.filter(r => r.success).length;
    console.log(`\n✅ Added vendor metafields to ${successfulProducts}/${productResults.length} products\n`);
    
    // Step 3: Test the metafields
    console.log('STEP 3: Testing vendor metafields');
    console.log('='.repeat(50));
    await manager.testVendorMetafields();
    
    console.log('\n🎉 Vendor metafield setup completed!');
    
  } catch (error) {
    console.error('❌ Error in vendor metafield setup:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = VendorMetafieldManager;
