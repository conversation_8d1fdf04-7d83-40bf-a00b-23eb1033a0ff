const { createAdminApiClient } = require('@shopify/admin-api-client');
const fs = require('fs').promises;
require('dotenv').config();

class VendorLookupService {
  constructor() {
    this.client = createAdminApiClient({
      storeDomain: process.env.SHOPIFY_STORE_URL,
      accessToken: process.env.SHOPIFY_ACCESS_TOKEN,
      apiVersion: process.env.SHOPIFY_API_VERSION || '2025-07'
    });
    
    // REAL Vendor contact database - verified from online sources
    this.vendorContacts = {
      'Canna River': {
        name: 'Canna River',
        email: '<EMAIL>',
        phone: '******-375-2777',
        website: 'https://www.cannariver.com',
        address: {
          street: '2535 Conejo Spectrum St',
          city: 'Thousand Oaks',
          state: 'CA',
          zip: '91320',
          country: 'USA'
        },
        businessHours: 'Mon-Fri 8AM-4:30PM PST',
        returnPolicy: 'https://www.cannariver.com/pages/60-days-form',
        contactPage: 'https://www.cannariver.com/pages/contact-us',
        notes: 'CBD products supplier - 60-day money-back guarantee, requires return authorization',
        verified: true,
        lastUpdated: '2025-07-15'
      },
      'GRAV®': {
        name: 'GRAV®',
        email: '<EMAIL>',
        phone: '************',
        website: 'https://grav.com',
        wholesiteWebsite: 'https://wholesale.grav.com',
        address: {
          street: '3501 Dime Circle, Ste 119',
          city: 'Austin',
          state: 'TX',
          zip: '78744',
          country: 'USA'
        },
        businessHours: 'Mon-Fri 8AM-6PM CST',
        returnPolicy: 'https://grav.com/pages/faqs-and-policies',
        contactPage: 'https://grav.com/pages/support',
        notes: 'Glass smoking accessories - Austin-based manufacturer, wholesale available',
        verified: true,
        lastUpdated: '2025-07-15'
      },
      'Sonny\'s Wellness': {
        name: 'Sonny\'s Wellness',
        email: '<EMAIL>',
        phone: 'Contact needed',
        website: 'https://sonnyswellness.com',
        address: {
          street: '811 Fort Salonga Road',
          city: 'Northport',
          state: 'NY',
          zip: '11731',
          country: 'USA'
        },
        businessHours: 'Contact needed',
        returnPolicy: 'https://sonnyswellness.com/policies/refund-policy',
        contactPage: 'https://sonnyswellness.com/pages/contact-us',
        notes: 'Eco-friendly CBD products - sustainable packaging, third-party tested',
        verified: true,
        lastUpdated: '2025-07-15'
      },
      'Smoke Drop': {
        name: 'Smoke Drop',
        email: '<EMAIL>',
        phone: 'Contact needed',
        website: 'https://smokedrop.com',
        address: {
          street: 'Address needed',
          city: 'City needed',
          state: 'State needed',
          zip: 'ZIP needed',
          country: 'USA'
        },
        businessHours: 'Contact needed',
        returnPolicy: 'Contact needed',
        notes: 'Vaping products supplier - contact info needs verification',
        verified: false,
        lastUpdated: '2025-07-15'
      },
      'ASH': {
        name: 'ASH',
        email: '<EMAIL>',
        phone: 'Contact needed',
        website: 'Contact needed',
        address: {
          street: 'Address needed',
          city: 'City needed',
          state: 'State needed',
          zip: 'ZIP needed',
          country: 'USA'
        },
        businessHours: 'Contact needed',
        returnPolicy: 'Contact needed',
        notes: 'Smoking accessories supplier - contact info needs verification',
        verified: false,
        lastUpdated: '2025-07-15'
      },
      'Discreet Smoker': {
        name: 'Discreet Smoker',
        email: '<EMAIL>',
        phone: 'Contact needed',
        website: 'Contact needed',
        address: {
          street: 'Address needed',
          city: 'City needed',
          state: 'State needed',
          zip: 'ZIP needed',
          country: 'USA'
        },
        businessHours: 'Contact needed',
        returnPolicy: 'Contact needed',
        notes: 'Discreet smoking products - contact info needs verification',
        verified: false,
        lastUpdated: '2025-07-15'
      }
    };
  }

  async getProductsByVendor(vendorName, limit = 10) {
    const query = `
      query($query: String!, $first: Int!) {
        products(query: $query, first: $first) {
          edges {
            node {
              id
              title
              vendor
              handle
              tags
              productType
              variants(first: 5) {
                edges {
                  node {
                    id
                    title
                    sku
                    barcode
                    price
                  }
                }
              }
            }
          }
        }
      }
    `;

    const variables = {
      query: `vendor:"${vendorName}"`,
      first: limit
    };

    try {
      const response = await this.client.request(query, { variables });
      return response.data?.products?.edges || [];
    } catch (error) {
      console.error(`Error fetching products for vendor ${vendorName}:`, error.message);
      return [];
    }
  }

  async getAllVendors() {
    const query = `
      query {
        products(first: 250) {
          edges {
            node {
              vendor
            }
          }
        }
      }
    `;

    try {
      const response = await this.client.request(query);
      const products = response.data?.products?.edges || [];
      
      // Extract unique vendors
      const vendors = [...new Set(
        products
          .map(edge => edge.node.vendor)
          .filter(vendor => vendor && vendor.trim() !== '')
      )];
      
      return vendors.sort();
    } catch (error) {
      console.error('Error fetching vendors:', error.message);
      return [];
    }
  }

  getVendorContactInfo(vendorName) {
    // Normalize vendor name for lookup
    const normalizedName = vendorName.trim();
    
    // Direct match
    if (this.vendorContacts[normalizedName]) {
      return this.vendorContacts[normalizedName];
    }
    
    // Fuzzy match for common variations
    const fuzzyMatches = {
      'smokedrop': 'Smoke Drop',
      'smoke drop': 'Smoke Drop',
      'cannariver': 'Canna River',
      'canna river': 'Canna River',
      'ash': 'ASH',
      'discreet smoker': 'Discreet Smoker',
      'discretesmoker': 'Discreet Smoker'
    };
    
    const fuzzyKey = normalizedName.toLowerCase();
    if (fuzzyMatches[fuzzyKey]) {
      return this.vendorContacts[fuzzyMatches[fuzzyKey]];
    }
    
    // Return default template for unknown vendors
    return {
      name: vendorName,
      email: `contact@${vendorName.toLowerCase().replace(/\s+/g, '')}.com`,
      phone: 'Contact needed',
      website: `https://${vendorName.toLowerCase().replace(/\s+/g, '')}.com`,
      address: {
        street: 'Address needed',
        city: 'City needed',
        state: 'State needed',
        zip: 'ZIP needed',
        country: 'USA'
      },
      businessHours: 'Business hours needed',
      returnPolicy: 'Return policy URL needed',
      notes: 'Contact information needs to be verified and updated'
    };
  }

  async generateVendorReport() {
    console.log('🔍 Generating comprehensive vendor report...\n');
    
    const vendors = await this.getAllVendors();
    console.log(`📋 Found ${vendors.length} unique vendors in store\n`);
    
    const report = {
      generatedAt: new Date().toISOString(),
      totalVendors: vendors.length,
      vendors: []
    };
    
    for (const vendorName of vendors) {
      console.log(`📦 Processing vendor: ${vendorName}`);
      
      const products = await this.getProductsByVendor(vendorName);
      const contactInfo = this.getVendorContactInfo(vendorName);
      
      const vendorData = {
        name: vendorName,
        productCount: products.length,
        products: products.map(edge => ({
          id: edge.node.id,
          title: edge.node.title,
          handle: edge.node.handle,
          tags: edge.node.tags,
          productType: edge.node.productType,
          variants: edge.node.variants.edges.map(variantEdge => ({
            id: variantEdge.node.id,
            title: variantEdge.node.title,
            sku: variantEdge.node.sku,
            barcode: variantEdge.node.barcode,
            price: variantEdge.node.price
          }))
        })),
        contactInfo
      };
      
      report.vendors.push(vendorData);
      
      console.log(`   ✅ ${products.length} products found`);
      console.log(`   📧 Contact: ${contactInfo.email}`);
      console.log(`   📞 Phone: ${contactInfo.phone}`);
      console.log(`   🌐 Website: ${contactInfo.website}\n`);
    }
    
    return report;
  }

  async saveReportToFile(report, filename = 'vendor-report.json') {
    try {
      await fs.writeFile(filename, JSON.stringify(report, null, 2));
      console.log(`💾 Report saved to ${filename}`);
      return filename;
    } catch (error) {
      console.error('Error saving report:', error.message);
      throw error;
    }
  }
}

// Main execution
async function main() {
  try {
    const vendorService = new VendorLookupService();
    
    // Generate comprehensive vendor report
    const report = await vendorService.generateVendorReport();
    
    // Save to JSON file
    const filename = `vendor-report-${new Date().toISOString().split('T')[0]}.json`;
    await vendorService.saveReportToFile(report, filename);
    
    console.log('\n' + '='.repeat(60));
    console.log('📊 VENDOR REPORT SUMMARY');
    console.log('='.repeat(60));
    console.log(`Total Vendors: ${report.totalVendors}`);
    console.log(`Report File: ${filename}`);
    console.log(`Generated: ${report.generatedAt}`);
    
    // Show vendor summary
    report.vendors.forEach(vendor => {
      console.log(`\n🏭 ${vendor.name}`);
      console.log(`   Products: ${vendor.productCount}`);
      console.log(`   Email: ${vendor.contactInfo.email}`);
      console.log(`   Phone: ${vendor.contactInfo.phone}`);
      console.log(`   Website: ${vendor.contactInfo.website}`);
    });
    
  } catch (error) {
    console.error('❌ Error generating vendor report:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = VendorLookupService;
