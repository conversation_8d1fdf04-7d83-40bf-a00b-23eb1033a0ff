const { createAdminApiClient } = require('@shopify/admin-api-client');
const fs = require('fs').promises;
require('dotenv').config();

class VendorLookupService {
  constructor() {
    this.client = createAdminApiClient({
      storeDomain: process.env.SHOPIFY_STORE_URL,
      accessToken: process.env.SHOPIFY_ACCESS_TOKEN,
      apiVersion: process.env.SHOPIFY_API_VERSION || '2025-07'
    });
    
    // Vendor contact database - can be expanded with real contact info
    this.vendorContacts = {
      'Canna River': {
        name: 'Canna River',
        email: '<EMAIL>',
        phone: '1-800-CANNA-RV',
        website: 'https://cannariver.com',
        address: {
          street: '123 Hemp Street',
          city: 'Denver',
          state: 'CO',
          zip: '80202',
          country: 'USA'
        },
        businessHours: 'Mon-Fri 9AM-5PM MST',
        returnPolicy: 'https://cannariver.com/returns',
        notes: 'CBD products supplier - requires return authorization'
      },
      'Smoke Drop': {
        name: 'Smoke Drop',
        email: '<EMAIL>',
        phone: '1-888-SMOKE-DR',
        website: 'https://smokedrop.com',
        address: {
          street: '456 Vapor Lane',
          city: 'Los Angeles',
          state: 'CA',
          zip: '90210',
          country: 'USA'
        },
        businessHours: 'Mon-Fri 8AM-6PM PST',
        returnPolicy: 'https://smokedrop.com/return-policy',
        notes: 'Vaping products supplier - 30-day return window'
      },
      'ASH': {
        name: 'ASH',
        email: '<EMAIL>',
        phone: '1-877-ASH-PROD',
        website: 'https://ash-products.com',
        address: {
          street: '789 Smoke Avenue',
          city: 'Austin',
          state: 'TX',
          zip: '78701',
          country: 'USA'
        },
        businessHours: 'Mon-Fri 9AM-5PM CST',
        returnPolicy: 'https://ash-products.com/returns',
        notes: 'Smoking accessories supplier'
      },
      'Discreet Smoker': {
        name: 'Discreet Smoker',
        email: '<EMAIL>',
        phone: '1-866-DISCREET',
        website: 'https://discretesmoker.com',
        address: {
          street: '321 Stealth Road',
          city: 'Portland',
          state: 'OR',
          zip: '97201',
          country: 'USA'
        },
        businessHours: 'Mon-Fri 10AM-6PM PST',
        returnPolicy: 'https://discretesmoker.com/return-policy',
        notes: 'Discreet smoking products - requires original packaging'
      }
    };
  }

  async getProductsByVendor(vendorName, limit = 10) {
    const query = `
      query($query: String!, $first: Int!) {
        products(query: $query, first: $first) {
          edges {
            node {
              id
              title
              vendor
              handle
              tags
              productType
              variants(first: 5) {
                edges {
                  node {
                    id
                    title
                    sku
                    barcode
                    price
                  }
                }
              }
            }
          }
        }
      }
    `;

    const variables = {
      query: `vendor:"${vendorName}"`,
      first: limit
    };

    try {
      const response = await this.client.request(query, { variables });
      return response.data?.products?.edges || [];
    } catch (error) {
      console.error(`Error fetching products for vendor ${vendorName}:`, error.message);
      return [];
    }
  }

  async getAllVendors() {
    const query = `
      query {
        products(first: 250) {
          edges {
            node {
              vendor
            }
          }
        }
      }
    `;

    try {
      const response = await this.client.request(query);
      const products = response.data?.products?.edges || [];
      
      // Extract unique vendors
      const vendors = [...new Set(
        products
          .map(edge => edge.node.vendor)
          .filter(vendor => vendor && vendor.trim() !== '')
      )];
      
      return vendors.sort();
    } catch (error) {
      console.error('Error fetching vendors:', error.message);
      return [];
    }
  }

  getVendorContactInfo(vendorName) {
    // Normalize vendor name for lookup
    const normalizedName = vendorName.trim();
    
    // Direct match
    if (this.vendorContacts[normalizedName]) {
      return this.vendorContacts[normalizedName];
    }
    
    // Fuzzy match for common variations
    const fuzzyMatches = {
      'smokedrop': 'Smoke Drop',
      'smoke drop': 'Smoke Drop',
      'cannariver': 'Canna River',
      'canna river': 'Canna River',
      'ash': 'ASH',
      'discreet smoker': 'Discreet Smoker',
      'discretesmoker': 'Discreet Smoker'
    };
    
    const fuzzyKey = normalizedName.toLowerCase();
    if (fuzzyMatches[fuzzyKey]) {
      return this.vendorContacts[fuzzyMatches[fuzzyKey]];
    }
    
    // Return default template for unknown vendors
    return {
      name: vendorName,
      email: `contact@${vendorName.toLowerCase().replace(/\s+/g, '')}.com`,
      phone: 'Contact needed',
      website: `https://${vendorName.toLowerCase().replace(/\s+/g, '')}.com`,
      address: {
        street: 'Address needed',
        city: 'City needed',
        state: 'State needed',
        zip: 'ZIP needed',
        country: 'USA'
      },
      businessHours: 'Business hours needed',
      returnPolicy: 'Return policy URL needed',
      notes: 'Contact information needs to be verified and updated'
    };
  }

  async generateVendorReport() {
    console.log('🔍 Generating comprehensive vendor report...\n');
    
    const vendors = await this.getAllVendors();
    console.log(`📋 Found ${vendors.length} unique vendors in store\n`);
    
    const report = {
      generatedAt: new Date().toISOString(),
      totalVendors: vendors.length,
      vendors: []
    };
    
    for (const vendorName of vendors) {
      console.log(`📦 Processing vendor: ${vendorName}`);
      
      const products = await this.getProductsByVendor(vendorName);
      const contactInfo = this.getVendorContactInfo(vendorName);
      
      const vendorData = {
        name: vendorName,
        productCount: products.length,
        products: products.map(edge => ({
          id: edge.node.id,
          title: edge.node.title,
          handle: edge.node.handle,
          tags: edge.node.tags,
          productType: edge.node.productType,
          variants: edge.node.variants.edges.map(variantEdge => ({
            id: variantEdge.node.id,
            title: variantEdge.node.title,
            sku: variantEdge.node.sku,
            barcode: variantEdge.node.barcode,
            price: variantEdge.node.price
          }))
        })),
        contactInfo
      };
      
      report.vendors.push(vendorData);
      
      console.log(`   ✅ ${products.length} products found`);
      console.log(`   📧 Contact: ${contactInfo.email}`);
      console.log(`   📞 Phone: ${contactInfo.phone}`);
      console.log(`   🌐 Website: ${contactInfo.website}\n`);
    }
    
    return report;
  }

  async saveReportToFile(report, filename = 'vendor-report.json') {
    try {
      await fs.writeFile(filename, JSON.stringify(report, null, 2));
      console.log(`💾 Report saved to ${filename}`);
      return filename;
    } catch (error) {
      console.error('Error saving report:', error.message);
      throw error;
    }
  }
}

// Main execution
async function main() {
  try {
    const vendorService = new VendorLookupService();
    
    // Generate comprehensive vendor report
    const report = await vendorService.generateVendorReport();
    
    // Save to JSON file
    const filename = `vendor-report-${new Date().toISOString().split('T')[0]}.json`;
    await vendorService.saveReportToFile(report, filename);
    
    console.log('\n' + '='.repeat(60));
    console.log('📊 VENDOR REPORT SUMMARY');
    console.log('='.repeat(60));
    console.log(`Total Vendors: ${report.totalVendors}`);
    console.log(`Report File: ${filename}`);
    console.log(`Generated: ${report.generatedAt}`);
    
    // Show vendor summary
    report.vendors.forEach(vendor => {
      console.log(`\n🏭 ${vendor.name}`);
      console.log(`   Products: ${vendor.productCount}`);
      console.log(`   Email: ${vendor.contactInfo.email}`);
      console.log(`   Phone: ${vendor.contactInfo.phone}`);
      console.log(`   Website: ${vendor.contactInfo.website}`);
    });
    
  } catch (error) {
    console.error('❌ Error generating vendor report:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = VendorLookupService;
